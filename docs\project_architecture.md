# 文件管理系统项目架构设计

## 项目概述

基于Sanic框架的文件管理系统，集成飞书认证，支持MySQL和Redis，具备横向扩展能力。

## 优化后的项目结构

```
backend/
├── app/
│   ├── __init__.py                    # 应用工厂
│   ├── blueprints/                    # 蓝图模块
│   │   ├── __init__.py
│   │   ├── auth/                      # 认证模块
│   │   │   ├── __init__.py
│   │   │   ├── routes.py              # 路由定义
│   │   │   ├── services.py            # 业务逻辑
│   │   │   ├── validators.py          # 数据验证
│   │   │   └── models.py              # 模型定义
│   │   ├── file_management/           # 文件管理模块
│   │   │   ├── __init__.py
│   │   │   ├── routes.py
│   │   │   ├── services.py
│   │   │   ├── validators.py
│   │   │   └── models.py
│   │   └── common/                    # 通用模块（新增）
│   │       ├── __init__.py
│   │       ├── routes.py              # 健康检查等通用接口
│   │       └── services.py
│   ├── core/                          # 核心组件（重构）
│   │   ├── __init__.py
│   │   ├── database.py                # 数据库连接管理
│   │   ├── redis_client.py            # Redis客户端
│   │   ├── auth_manager.py            # 认证管理器
│   │   ├── config.py                  # 配置管理
│   │   └── exceptions.py              # 自定义异常
│   ├── middleware/                    # 中间件
│   │   ├── __init__.py
│   │   ├── auth_middleware.py         # 认证中间件
│   │   ├── cors_middleware.py         # CORS中间件
│   │   ├── error_middleware.py        # 错误处理中间件
│   │   └── logging_middleware.py      # 日志中间件
│   ├── utils/                         # 工具类
│   │   ├── __init__.py
│   │   ├── response_helper.py         # 响应格式化
│   │   ├── feishu_client.py          # 飞书API客户端
│   │   ├── file_helper.py            # 文件处理工具
│   │   ├── decorators.py             # 装饰器
│   │   └── validators.py             # 通用验证器
│   └── schemas/                       # 数据模式（新增）
│       ├── __init__.py
│       ├── auth_schemas.py
│       ├── file_schemas.py
│       └── common_schemas.py
├── config/                            # 配置文件
│   ├── __init__.py
│   ├── base.py                        # 基础配置
│   ├── development.py                 # 开发环境配置
│   ├── production.py                  # 生产环境配置
│   └── testing.py                     # 测试环境配置
├── migrations/                        # 数据库迁移
│   ├── __init__.py
│   └── versions/
├── tests/                             # 测试目录
│   ├── __init__.py
│   ├── conftest.py
│   ├── unit/
│   └── integration/
├── scripts/                           # 脚本目录
│   ├── init_db.py
│   ├── seed_data.py
│   └── start_server.py
├── docs/                              # 文档目录
│   ├── api/                           # API文档
│   ├── development.md                 # 开发文档
│   └── deployment.md                  # 部署文档
├── logs/                              # 日志目录（新增）
├── uploads/                           # 文件上传目录（新增）
├── requirements/                      # 依赖管理（优化）
│   ├── base.txt                       # 基础依赖
│   ├── development.txt                # 开发依赖
│   └── production.txt                 # 生产依赖
├── .env.example                       # 环境变量示例
├── .gitignore
├── Dockerfile
├── docker-compose.yml
├── docker-compose.dev.yml
├── alembic.ini                        # 数据库迁移配置
├── pytest.ini                        # 测试配置
├── run.py                             # 应用启动入口
└── README.md
```

## 架构优化说明

### 1. 模块化重构
- **core模块**：集中管理核心组件，提高代码复用性
- **schemas模块**：统一数据序列化和验证
- **common蓝图**：处理通用接口，如健康检查、系统信息等

### 2. 配置管理优化
- 分环境配置文件，支持不同部署环境
- 环境变量统一管理，提高安全性

### 3. 依赖管理优化
- 分层依赖管理，区分基础、开发、生产依赖
- 便于容器化部署和环境隔离

### 4. 扩展性设计
- 蓝图模式支持模块化扩展
- 中间件架构支持功能插件化
- 统一的响应格式和错误处理

## 技术栈选择

- **Web框架**：Sanic (异步高性能)
- **数据库**：MySQL 8.0+ (主数据存储)
- **缓存**：Redis 6.0+ (会话管理、缓存)
- **ORM**：SQLAlchemy + Alembic (数据库操作和迁移)
- **认证**：JWT + 飞书OAuth (用户认证)
- **序列化**：Pydantic (数据验证和序列化)
- **测试**：Pytest (单元测试和集成测试)
- **部署**：Docker + Docker Compose

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    feishu_open_id VARCHAR(64) UNIQUE NOT NULL COMMENT '飞书OpenID',
    feishu_user_id VARCHAR(64) UNIQUE NOT NULL COMMENT '飞书UserID',
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(255) COMMENT '邮箱',
    avatar VARCHAR(500) COMMENT '头像URL',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_feishu_open_id (feishu_open_id),
    INDEX idx_feishu_user_id (feishu_user_id)
);
```

### 文件表 (files)
```sql
CREATE TABLE files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    md5_hash VARCHAR(32) COMMENT 'MD5哈希值',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (uploader_id) REFERENCES users(id),
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_md5_hash (md5_hash),
    INDEX idx_created_at (created_at)
);
```

### 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL COMMENT 'Session Token',
    refresh_token VARCHAR(255) UNIQUE NOT NULL COMMENT 'Refresh Token',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
);
```

## API接口设计规范

### 认证相关接口
- `GET /api/auth/get_appid` - 获取飞书应用ID
- `GET /api/auth/callback` - 飞书OAuth回调
- `GET /api/auth/validate` - 验证token有效性
- `POST /api/auth/refresh` - 刷新token
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/user` - 获取当前用户信息

### 文件管理接口
- `POST /api/files/upload` - 文件上传
- `GET /api/files` - 文件列表
- `GET /api/files/{id}` - 获取文件详情
- `PUT /api/files/{id}` - 更新文件信息
- `DELETE /api/files/{id}` - 删除文件
- `GET /api/files/{id}/download` - 文件下载

### 通用接口
- `GET /api/health` - 健康检查
- `GET /api/version` - 版本信息

## 响应格式规范

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息",
    "timestamp": "2024-01-01T00:00:00Z"
}
```