"""
Redis客户端管理模块
提供Redis连接和缓存操作
"""
import redis
import json
import logging
from typing import Any, Optional, Union
from datetime import timedelta

from config import config

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis管理器"""

    def __init__(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                config.REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None

    def is_connected(self) -> bool:
        """
        检查Redis连接状态

        Returns:
            bool: 连接状态
        """
        if not self.redis_client:
            return False

        try:
            self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接检查失败: {e}")
            return False

    def set(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒或timedelta对象）

        Returns:
            bool: 操作是否成功
        """
        if not self.is_connected():
            return False

        try:
            # 序列化复杂对象
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)

            result = self.redis_client.set(key, value, ex=expire)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis设置值失败: {e}")
            return False

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存值或None
        """
        if not self.is_connected():
            return None

        try:
            value = self.redis_client.get(key)
            if value is None:
                return None

            # 尝试反序列化JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Redis获取值失败: {e}")
            return None

    def delete(self, *keys: str) -> int:
        """
        删除缓存键

        Args:
            keys: 要删除的键

        Returns:
            int: 删除的键数量
        """
        if not self.is_connected():
            return 0

        try:
            return self.redis_client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis删除键失败: {e}")
            return 0

    def exists(self, key: str) -> bool:
        """
        检查键是否存在

        Args:
            key: 缓存键

        Returns:
            bool: 键是否存在
        """
        if not self.is_connected():
            return False

        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Redis检查键存在失败: {e}")
            return False

    def expire(self, key: str, time: Union[int, timedelta]) -> bool:
        """
        设置键的过期时间

        Args:
            key: 缓存键
            time: 过期时间（秒或timedelta对象）

        Returns:
            bool: 操作是否成功
        """
        if not self.is_connected():
            return False

        try:
            return bool(self.redis_client.expire(key, time))
        except Exception as e:
            logger.error(f"Redis设置过期时间失败: {e}")
            return False


# 创建Redis管理器实例
redis_manager = RedisManager()