"""
认证管理器
处理JWT token生成、验证和用户会话管理
"""
import jwt
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError

from config import config
from .redis_client import redis_manager
from .exceptions import AuthenticationError, TokenExpiredError

logger = logging.getLogger(__name__)


class AuthManager:
    """认证管理器"""

    def __init__(self):
        self.secret_key = config.JWT_SECRET_KEY
        self.algorithm = config.JWT_ALGORITHM
        self.access_token_expire_minutes = config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = config.JWT_REFRESH_TOKEN_EXPIRE_DAYS

    def generate_access_token(self, user_data: Dict[str, Any]) -> str:
        """
        生成访问令牌

        Args:
            user_data: 用户数据字典

        Returns:
            str: JWT访问令牌
        """
        try:
            # 设置过期时间
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)

            # 构建payload
            payload = {
                "user_id": user_data.get("id"),
                "feishu_open_id": user_data.get("feishu_open_id"),
                "name": user_data.get("name"),
                "exp": expire,
                "iat": datetime.utcnow(),
                "type": "access"
            }

            # 生成token
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            logger.info(f"为用户 {user_data.get('name')} 生成访问令牌")
            return token

        except Exception as e:
            logger.error(f"生成访问令牌失败: {e}")
            raise AuthenticationError("生成访问令牌失败")

    def generate_refresh_token(self, user_id: int) -> str:
        """
        生成刷新令牌

        Args:
            user_id: 用户ID

        Returns:
            str: 刷新令牌
        """
        try:
            # 生成唯一的刷新令牌
            refresh_token = str(uuid.uuid4())

            # 设置过期时间
            expire_seconds = self.refresh_token_expire_days * 24 * 3600

            # 在Redis中存储刷新令牌
            redis_key = f"refresh_token:{refresh_token}"
            redis_manager.set(redis_key, user_id, expire=expire_seconds)

            logger.info(f"为用户 {user_id} 生成刷新令牌")
            return refresh_token

        except Exception as e:
            logger.error(f"生成刷新令牌失败: {e}")
            raise AuthenticationError("生成刷新令牌失败")

    def verify_access_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证访问令牌

        Args:
            token: JWT访问令牌

        Returns:
            Dict: 用户信息字典或None
        """
        try:
            # 解码token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # 检查token类型
            if payload.get("type") != "access":
                raise AuthenticationError("无效的令牌类型")

            # 检查是否在黑名单中
            if self._is_token_blacklisted(token):
                raise AuthenticationError("令牌已被撤销")

            return payload

        except jwt.ExpiredSignatureError:
            logger.warning("访问令牌已过期")
            raise TokenExpiredError("访问令牌已过期")
        except JWTError as e:
            logger.warning(f"访问令牌验证失败: {e}")
            raise AuthenticationError("无效的访问令牌")
        except Exception as e:
            logger.error(f"验证访问令牌时发生错误: {e}")
            raise AuthenticationError("令牌验证失败")

    def verify_refresh_token(self, refresh_token: str) -> Optional[int]:
        """
        验证刷新令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            int: 用户ID或None
        """
        try:
            redis_key = f"refresh_token:{refresh_token}"
            user_id = redis_manager.get(redis_key)

            if user_id is None:
                raise AuthenticationError("无效或已过期的刷新令牌")

            return int(user_id)

        except Exception as e:
            logger.error(f"验证刷新令牌失败: {e}")
            raise AuthenticationError("刷新令牌验证失败")

    def revoke_refresh_token(self, refresh_token: str) -> bool:
        """
        撤销刷新令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            bool: 操作是否成功
        """
        try:
            redis_key = f"refresh_token:{refresh_token}"
            result = redis_manager.delete(redis_key)

            if result > 0:
                logger.info(f"刷新令牌已撤销: {refresh_token}")
                return True
            return False

        except Exception as e:
            logger.error(f"撤销刷新令牌失败: {e}")
            return False

    def blacklist_token(self, token: str) -> bool:
        """
        将访问令牌加入黑名单

        Args:
            token: 访问令牌

        Returns:
            bool: 操作是否成功
        """
        try:
            # 解码token获取过期时间
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm], options={"verify_exp": False})
            exp = payload.get("exp")

            if exp:
                # 计算剩余过期时间
                expire_time = datetime.fromtimestamp(exp) - datetime.utcnow()
                if expire_time.total_seconds() > 0:
                    redis_key = f"blacklist_token:{token}"
                    redis_manager.set(redis_key, "1", expire=int(expire_time.total_seconds()))
                    logger.info("访问令牌已加入黑名单")
                    return True

            return False

        except Exception as e:
            logger.error(f"将令牌加入黑名单失败: {e}")
            return False

    def _is_token_blacklisted(self, token: str) -> bool:
        """
        检查令牌是否在黑名单中

        Args:
            token: 访问令牌

        Returns:
            bool: 是否在黑名单中
        """
        try:
            redis_key = f"blacklist_token:{token}"
            return redis_manager.exists(redis_key)
        except Exception:
            return False


# 创建认证管理器实例
auth_manager = AuthManager()