"""
自定义异常类
定义系统中使用的各种异常
"""


class BaseAPIException(Exception):
    """API基础异常类"""

    def __init__(self, message: str, code: int = 500, details: str = None):
        self.message = message
        self.code = code
        self.details = details
        super().__init__(self.message)


class AuthenticationError(BaseAPIException):
    """认证错误异常"""

    def __init__(self, message: str = "认证失败", details: str = None):
        super().__init__(message, code=401, details=details)


class AuthorizationError(BaseAPIException):
    """授权错误异常"""

    def __init__(self, message: str = "权限不足", details: str = None):
        super().__init__(message, code=403, details=details)


class TokenExpiredError(BaseAPIException):
    """令牌过期异常"""

    def __init__(self, message: str = "令牌已过期", details: str = None):
        super().__init__(message, code=401, details=details)


class ValidationError(BaseAPIException):
    """数据验证错误异常"""

    def __init__(self, message: str = "数据验证失败", details: str = None):
        super().__init__(message, code=400, details=details)


class NotFoundError(BaseAPIException):
    """资源不存在异常"""

    def __init__(self, message: str = "资源不存在", details: str = None):
        super().__init__(message, code=404, details=details)


class ConflictError(BaseAPIException):
    """资源冲突异常"""

    def __init__(self, message: str = "资源冲突", details: str = None):
        super().__init__(message, code=409, details=details)


class DatabaseError(BaseAPIException):
    """数据库错误异常"""

    def __init__(self, message: str = "数据库操作失败", details: str = None):
        super().__init__(message, code=500, details=details)


class ExternalServiceError(BaseAPIException):
    """外部服务错误异常"""

    def __init__(self, message: str = "外部服务调用失败", details: str = None):
        super().__init__(message, code=502, details=details)


class FileUploadError(BaseAPIException):
    """文件上传错误异常"""

    def __init__(self, message: str = "文件上传失败", details: str = None):
        super().__init__(message, code=400, details=details)


class FileSizeError(BaseAPIException):
    """文件大小错误异常"""

    def __init__(self, message: str = "文件大小超出限制", details: str = None):
        super().__init__(message, code=413, details=details)


class FileTypeError(BaseAPIException):
    """文件类型错误异常"""

    def __init__(self, message: str = "不支持的文件类型", details: str = None):
        super().__init__(message, code=415, details=details)