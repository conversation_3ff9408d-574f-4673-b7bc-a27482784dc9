import { request, requestWithoutAuth } from '@/utils/request'

/**
 * 认证相关API接口
 */

/**
 * 获取应用ID
 * @returns {Promise<Object>} 包含appid的响应对象
 */
export async function getAppId() {
  try {
    const response = await requestWithoutAuth({
      url: '/auth/get_appid',
      method: 'GET'
    })
    return response.data
  } catch (error) {
    console.error('获取应用ID失败:', error)
    throw new Error('获取应用ID失败，请检查网络连接')
  }
}

/**
 * 飞书回调接口，通过授权码获取用户信息和session
 * @param {string} code - 飞书授权码
 * @returns {Promise<Object>} 包含用户信息和session token的对象
 */
export async function feishuCallback(code) {
  try {
    if (!code) {
      throw new Error('授权码不能为空')
    }

    const response = await requestWithoutAuth({
      url: '/auth/callback',
      method: 'GET',
      params: { code }
    })
    
    return response.data
  } catch (error) {
    console.error('飞书回调失败:', error)
    
    // 根据错误类型提供更具体的错误信息
    if (error.response?.status === 401) {
      throw new Error('授权失败，请重新登录')
    } else if (error.response?.status === 403) {
      throw new Error('没有访问权限')
    } else if (error.response?.status >= 500) {
      throw new Error('服务器错误，请稍后重试')
    } else {
      throw new Error('获取用户信息失败，请重试')
    }
  }
}

/**
 * 验证session token有效性
 * @param {string} sessionToken - session token
 * @returns {Promise<Object>} 用户信息或null
 */
export async function validateSession(sessionToken) {
  try {
    if (!sessionToken) {
      return null
    }

    const response = await request({
      url: '/auth/validate',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${sessionToken}`
      },
      skipAuth: true // 手动指定token，跳过自动添加
    })
    return response.data
  } catch (error) {
    console.error('验证session失败:', error)
    // session无效时返回null而不是抛出错误
    return null
  }
}

/**
 * 刷新session token
 * @param {string} refreshToken - refresh token
 * @returns {Promise<Object>} 新的session信息
 */
export async function refreshSession(refreshToken) {
  try {
    if (!refreshToken) {
      throw new Error('Refresh token不能为空')
    }

    const response = await requestWithoutAuth({
      url: '/auth/refresh',
      method: 'POST',
      data: { refresh_token: refreshToken }
    })
    
    return response.data
  } catch (error) {
    console.error('刷新session失败:', error)
    throw new Error('刷新登录状态失败，请重新登录')
  }
}

/**
 * 用户登出
 * @param {string} sessionToken - session token
 * @returns {Promise<boolean>} 登出是否成功
 */
export async function logout(sessionToken) {
  try {
    await request({
      url: '/auth/logout',
      method: 'POST',
      headers: sessionToken ? {
        'Authorization': `Bearer ${sessionToken}`
      } : {},
      skipAuth: true // 手动指定token，跳过自动添加
    })
    return true
  } catch (error) {
    console.error('登出失败:', error)
    // 即使登出失败也返回true，因为可能是token已过期
    return true
  }
}

/**
 * 获取当前用户信息
 * @param {string} sessionToken - session token（可选，不传则使用自动管理的token）
 * @returns {Promise<Object>} 用户信息
 */
export async function getCurrentUser(sessionToken = null) {
  try {
    const config = {
      url: '/auth/user',
      method: 'GET'
    }
    
    // 如果手动指定了token，则使用手动指定的
    if (sessionToken) {
      config.headers = {
        'Authorization': `Bearer ${sessionToken}`
      }
      config.skipAuth = true
    }
    // 否则使用自动管理的token（默认行为）

    const response = await request(config)
    return response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw new Error('获取用户信息失败')
  }
}
