"""
装饰器工具
提供认证、权限检查等装饰器
"""
import functools
import logging
from typing import Callable, Any
from sanic import Request
from sanic.response import JSONResponse
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth_manager import auth_manager
from app.core.exceptions import AuthenticationError, BaseAPIException
from app.utils.response_helper import error_response
from app.blueprints.auth.services import user_service

logger = logging.getLogger(__name__)


def require_auth(func: Callable) -> Callable:
    """
    认证装饰器
    验证用户是否已登录，并将用户信息注入到request.ctx中

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    async def wrapper(request: Request, *args, **kwargs) -> Any:
        try:
            # 获取Authorization头
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                raise AuthenticationError("缺少有效的认证令牌")

            # 提取token
            token = auth_header.split(" ")[1]

            # 验证token
            payload = auth_manager.verify_access_token(token)
            user_id = payload.get("user_id")

            if not user_id:
                raise AuthenticationError("无效的令牌")

            # 获取用户信息
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                user = user_service.get_user_by_id(user_id, db)
                if not user:
                    raise AuthenticationError("用户不存在")

                # 将用户信息注入到request.ctx中
                request.ctx.user = user.to_dict()
                request.ctx.user_id = user.id

                # 调用原函数
                return await func(request, *args, **kwargs)

            finally:
                db.close()

        except BaseAPIException as e:
            logger.warning(f"认证失败: {e.message}")
            return error_response(message=e.message, code=e.code)
        except Exception as e:
            logger.error(f"认证时发生未知错误: {e}")
            return error_response(message="认证失败", code=401)

    return wrapper


def require_permission(permission: str):
    """
    权限检查装饰器
    检查用户是否具有指定权限

    Args:
        permission: 所需权限

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(request: Request, *args, **kwargs) -> Any:
            try:
                # 首先检查用户是否已认证
                user_info = getattr(request.ctx, 'user', None)
                if not user_info:
                    raise AuthenticationError("用户未认证")

                # TODO: 实现权限检查逻辑
                # 这里可以根据用户角色、权限表等进行权限验证
                # 暂时允许所有已认证用户访问

                return await func(request, *args, **kwargs)

            except BaseAPIException as e:
                logger.warning(f"权限检查失败: {e.message}")
                return error_response(message=e.message, code=e.code)
            except Exception as e:
                logger.error(f"权限检查时发生未知错误: {e}")
                return error_response(message="权限不足", code=403)

        return wrapper
    return decorator


def validate_json(required_fields: list = None):
    """
    JSON数据验证装饰器
    验证请求体中是否包含必需的字段

    Args:
        required_fields: 必需字段列表

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(request: Request, *args, **kwargs) -> Any:
            try:
                # 检查Content-Type
                content_type = request.headers.get("Content-Type", "")
                if not content_type.startswith("application/json"):
                    return error_response(message="请求头Content-Type必须为application/json", code=400)

                # 获取JSON数据
                try:
                    data = request.json
                except Exception:
                    return error_response(message="无效的JSON格式", code=400)

                if data is None:
                    return error_response(message="请求体不能为空", code=400)

                # 验证必需字段
                if required_fields:
                    missing_fields = []
                    for field in required_fields:
                        if field not in data or data[field] is None:
                            missing_fields.append(field)

                    if missing_fields:
                        return error_response(
                            message=f"缺少必需字段: {', '.join(missing_fields)}",
                            code=400
                        )

                # 将验证后的数据注入到request.ctx中
                request.ctx.validated_data = data

                return await func(request, *args, **kwargs)

            except Exception as e:
                logger.error(f"JSON验证时发生错误: {e}")
                return error_response(message="数据验证失败", code=400)

        return wrapper
    return decorator