"""
认证相关数据模型
包含用户表和用户会话表的定义
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from app.core.database import Base


class User(Base):
    """用户表模型"""

    __tablename__ = "users"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="用户ID")

    # 飞书相关字段
    feishu_open_id = Column(String(64), unique=True, nullable=False, comment="飞书OpenID")
    feishu_user_id = Column(String(64), unique=True, nullable=False, comment="飞书UserID")

    # 用户基本信息
    name = Column(String(100), nullable=False, comment="用户姓名")
    email = Column(String(255), nullable=True, comment="邮箱")
    avatar = Column(String(500), nullable=True, comment="头像URL")
    department = Column(String(100), nullable=True, comment="部门")

    # 状态字段
    status = Column(Integer, default=1, comment="状态：1-正常，0-禁用")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    files = relationship("File", back_populates="uploader", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index("idx_feishu_open_id", "feishu_open_id"),
        Index("idx_feishu_user_id", "feishu_user_id"),
        Index("idx_status", "status"),
    )

    def __repr__(self):
        return f"<User(id={self.id}, name='{self.name}', feishu_open_id='{self.feishu_open_id}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "feishu_open_id": self.feishu_open_id,
            "feishu_user_id": self.feishu_user_id,
            "name": self.name,
            "email": self.email,
            "avatar": self.avatar,
            "department": self.department,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class UserSession(Base):
    """用户会话表模型"""

    __tablename__ = "user_sessions"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="会话ID")

    # 外键
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")

    # 会话信息
    session_token = Column(String(255), unique=True, nullable=False, comment="Session Token")
    refresh_token = Column(String(255), unique=True, nullable=False, comment="Refresh Token")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    user = relationship("User", back_populates="sessions")

    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_session_token", "session_token"),
        Index("idx_refresh_token", "refresh_token"),
        Index("idx_expires_at", "expires_at"),
    )

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, expires_at='{self.expires_at}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "session_token": self.session_token,
            "refresh_token": self.refresh_token,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }