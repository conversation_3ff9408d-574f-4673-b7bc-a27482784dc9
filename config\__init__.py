"""
配置管理模块
根据环境变量自动选择对应的配置类
"""
import os
from .base import BaseConfig
from .development import DevelopmentConfig
from .production import ProductionConfig


def get_config():
    """
    根据环境变量获取对应的配置类

    Returns:
        配置类实例
    """
    env = os.getenv("ENVIRONMENT", "development").lower()

    config_map = {
        "development": DevelopmentConfig,
        "production": ProductionConfig,
        "testing": BaseConfig,  # 测试环境使用基础配置
    }

    config_class = config_map.get(env, DevelopmentConfig)
    return config_class()


# 导出配置实例
config = get_config()