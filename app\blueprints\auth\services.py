"""
认证服务模块
处理用户认证、登录、登出等业务逻辑
"""
import requests
import logging
from typing import Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from config import config
from app.core.database import get_db
from app.core.auth_manager import auth_manager
from app.core.exceptions import (
    AuthenticationError, ExternalServiceError,
    DatabaseError, NotFoundError
)
from .models import User, UserSession

logger = logging.getLogger(__name__)


class FeishuAuthService:
    """飞书认证服务"""

    def __init__(self):
        self.app_id = config.FEISHU_APP_ID
        self.app_secret = config.FEISHU_APP_SECRET
        self.feishu_host = config.FEISHU_HOST
        self._app_access_token = None

    def get_app_access_token(self) -> str:
        """
        获取应用访问令牌

        Returns:
            str: 应用访问令牌
        """
        try:
            url = f"{self.feishu_host}/open-apis/auth/v3/app_access_token/internal"
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }

            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()

            result = response.json()
            if result.get("code") != 0:
                raise ExternalServiceError(f"获取应用访问令牌失败: {result.get('msg')}")

            self._app_access_token = result.get("app_access_token")
            logger.info("获取应用访问令牌成功")
            return self._app_access_token

        except requests.RequestException as e:
            logger.error(f"请求飞书API失败: {e}")
            raise ExternalServiceError("飞书服务连接失败")
        except Exception as e:
            logger.error(f"获取应用访问令牌失败: {e}")
            raise ExternalServiceError("获取应用访问令牌失败")

    def get_user_access_token(self, code: str) -> str:
        """
        通过授权码获取用户访问令牌

        Args:
            code: 授权码

        Returns:
            str: 用户访问令牌
        """
        try:
            # 确保有应用访问令牌
            if not self._app_access_token:
                self.get_app_access_token()

            url = f"{self.feishu_host}/open-apis/authen/v1/access_token"
            headers = {
                "Authorization": f"Bearer {self._app_access_token}",
                "Content-Type": "application/json"
            }
            payload = {
                "grant_type": "authorization_code",
                "code": code
            }

            response = requests.post(url, json=payload, headers=headers, timeout=10)
            response.raise_for_status()

            result = response.json()
            if result.get("code") != 0:
                raise ExternalServiceError(f"获取用户访问令牌失败: {result.get('msg')}")

            user_access_token = result.get("data", {}).get("access_token")
            if not user_access_token:
                raise ExternalServiceError("用户访问令牌为空")

            logger.info("获取用户访问令牌成功")
            return user_access_token

        except requests.RequestException as e:
            logger.error(f"请求飞书API失败: {e}")
            raise ExternalServiceError("飞书服务连接失败")
        except Exception as e:
            logger.error(f"获取用户访问令牌失败: {e}")
            raise ExternalServiceError("获取用户访问令牌失败")

    def get_user_info(self, user_access_token: str) -> Dict[str, Any]:
        """
        获取用户信息

        Args:
            user_access_token: 用户访问令牌

        Returns:
            Dict: 用户信息
        """
        try:
            url = f"{self.feishu_host}/open-apis/authen/v1/user_info"
            headers = {
                "Authorization": f"Bearer {user_access_token}",
                "Content-Type": "application/json"
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            result = response.json()
            if result.get("code") != 0:
                raise ExternalServiceError(f"获取用户信息失败: {result.get('msg')}")

            user_data = result.get("data", {})
            logger.info(f"获取用户信息成功: {user_data.get('name')}")
            return user_data

        except requests.RequestException as e:
            logger.error(f"请求飞书API失败: {e}")
            raise ExternalServiceError("飞书服务连接失败")
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            raise ExternalServiceError("获取用户信息失败")


class UserService:
    """用户服务"""

    def __init__(self):
        self.feishu_service = FeishuAuthService()

    def create_or_update_user(self, feishu_user_data: Dict[str, Any], db: Session) -> User:
        """
        创建或更新用户信息

        Args:
            feishu_user_data: 飞书用户数据
            db: 数据库会话

        Returns:
            User: 用户对象
        """
        try:
            feishu_open_id = feishu_user_data.get("open_id")
            feishu_user_id = feishu_user_data.get("user_id")

            if not feishu_open_id or not feishu_user_id:
                raise AuthenticationError("飞书用户ID信息不完整")

            # 查找现有用户
            user = db.query(User).filter(User.feishu_open_id == feishu_open_id).first()

            if user:
                # 更新现有用户信息
                user.name = feishu_user_data.get("name", user.name)
                user.email = feishu_user_data.get("email", user.email)
                user.avatar = feishu_user_data.get("avatar_url", user.avatar)
                user.updated_at = datetime.utcnow()
                logger.info(f"更新用户信息: {user.name}")
            else:
                # 创建新用户
                user = User(
                    feishu_open_id=feishu_open_id,
                    feishu_user_id=feishu_user_id,
                    name=feishu_user_data.get("name", ""),
                    email=feishu_user_data.get("email"),
                    avatar=feishu_user_data.get("avatar_url"),
                    department=feishu_user_data.get("department_name"),
                    status=1
                )
                db.add(user)
                logger.info(f"创建新用户: {user.name}")

            db.commit()
            db.refresh(user)
            return user

        except Exception as e:
            db.rollback()
            logger.error(f"创建或更新用户失败: {e}")
            raise DatabaseError("用户信息保存失败")

    def get_user_by_id(self, user_id: int, db: Session) -> Optional[User]:
        """
        根据用户ID获取用户信息

        Args:
            user_id: 用户ID
            db: 数据库会话

        Returns:
            User: 用户对象或None
        """
        try:
            return db.query(User).filter(User.id == user_id, User.status == 1).first()
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None

    def get_user_by_feishu_id(self, feishu_open_id: str, db: Session) -> Optional[User]:
        """
        根据飞书OpenID获取用户信息

        Args:
            feishu_open_id: 飞书OpenID
            db: 数据库会话

        Returns:
            User: 用户对象或None
        """
        try:
            return db.query(User).filter(
                User.feishu_open_id == feishu_open_id,
                User.status == 1
            ).first()
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None

    def authenticate_user(self, code: str, db: Session) -> Tuple[User, str, str]:
        """
        用户认证流程

        Args:
            code: 飞书授权码
            db: 数据库会话

        Returns:
            Tuple[User, str, str]: 用户对象、访问令牌、刷新令牌
        """
        try:
            # 1. 获取用户访问令牌
            user_access_token = self.feishu_service.get_user_access_token(code)

            # 2. 获取用户信息
            feishu_user_data = self.feishu_service.get_user_info(user_access_token)

            # 3. 创建或更新用户
            user = self.create_or_update_user(feishu_user_data, db)

            # 4. 生成JWT令牌
            access_token = auth_manager.generate_access_token(user.to_dict())
            refresh_token = auth_manager.generate_refresh_token(user.id)

            # 5. 保存会话信息
            self._save_user_session(user.id, access_token, refresh_token, db)

            logger.info(f"用户认证成功: {user.name}")
            return user, access_token, refresh_token

        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            raise

    def _save_user_session(self, user_id: int, access_token: str, refresh_token: str, db: Session):
        """
        保存用户会话信息

        Args:
            user_id: 用户ID
            access_token: 访问令牌
            refresh_token: 刷新令牌
            db: 数据库会话
        """
        try:
            # 计算过期时间
            expires_at = datetime.utcnow() + timedelta(days=config.JWT_REFRESH_TOKEN_EXPIRE_DAYS)

            # 删除旧的会话
            db.query(UserSession).filter(UserSession.user_id == user_id).delete()

            # 创建新会话
            session = UserSession(
                user_id=user_id,
                session_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at
            )

            db.add(session)
            db.commit()
            logger.info(f"保存用户会话: user_id={user_id}")

        except Exception as e:
            db.rollback()
            logger.error(f"保存用户会话失败: {e}")
            raise DatabaseError("会话保存失败")


# 创建服务实例
feishu_auth_service = FeishuAuthService()
user_service = UserService()