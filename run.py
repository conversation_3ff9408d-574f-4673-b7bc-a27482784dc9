"""
应用启动入口
启动Sanic应用服务器
"""
import logging
from app import create_app
from config import config

# 创建应用实例
app = create_app()

if __name__ == "__main__":
    logger = logging.getLogger(__name__)

    try:
        # 验证配置
        config.validate_config()
        logger.info("配置验证通过")

        # 启动服务器
        logger.info(f"启动文件管理系统服务器...")
        logger.info(f"服务器地址: http://{config.HOST}:{config.PORT}")
        logger.info(f"环境模式: {'开发' if config.DEBUG else '生产'}")

        app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            auto_reload=config.DEBUG,
            access_log=config.DEBUG
        )

    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        exit(1)