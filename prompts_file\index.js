/**
 * 飞书网页应用主要逻辑
 * 包含用户认证、信息展示和交互效果
 */

let lang = window.navigator.language;
console.log(lang);

/**
 * 显示或隐藏加载动画
 * @param {boolean} show - 是否显示加载动画
 */
function showLoader(show) {
    const loader = document.getElementById('loader');
    if (show) {
        loader.style.display = 'flex';
        // 添加淡入效果
        setTimeout(() => {
            loader.style.opacity = '1';
        }, 10);
    } else {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.style.display = 'none';
        }, 300);
    }
}

/**
 * 显示或隐藏警告信息
 * @param {boolean} show - 是否显示警告信息
 */
function showWarning(show) {
    const warning = document.getElementById('warning');
    const mainContainer = document.querySelector('.main-container');
    
    if (show) {
        warning.style.display = 'block';
        if (mainContainer) {
            mainContainer.style.display = 'none';
        }
        // 添加渐显动画
        setTimeout(() => {
            warning.style.opacity = '1';
            warning.style.transform = 'translateY(0)';
        }, 10);
    } else {
        warning.style.opacity = '0';
        warning.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            warning.style.display = 'none';
            if (mainContainer) {
                mainContainer.style.display = 'flex';
            }
        }, 300);
    }
}

/**
 * 添加页面进入动画
 */
function initPageAnimations() {
    const mainContainer = document.querySelector('.main-container');
    const icon = document.querySelector('.icon');
    
    if (mainContainer) {
        mainContainer.style.opacity = '0';
        mainContainer.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            mainContainer.style.transition = 'all 0.6s ease';
            mainContainer.style.opacity = '1';
            mainContainer.style.transform = 'translateY(0)';
        }, 100);
    }
    
    if (icon) {
        icon.style.opacity = '0';
        icon.style.transform = 'translateX(-50%) translateY(20px)';
        
        setTimeout(() => {
            icon.style.transition = 'all 0.8s ease';
            icon.style.opacity = '0.8';
            icon.style.transform = 'translateX(-50%) translateY(0)';
        }, 500);
    }
}

/**
 * 处理API认证流程
 */
function apiAuth() {
    if (!window.h5sdk) {
        console.log('invalid h5sdk');
        showWarning(true);
        return;
    }

    showLoader(true);

    fetch(`/api/auth/get_appid`)
        .then(response1 => response1.json())
        .then(res1 => {
            console.log("get appid succeed: ", res1.appid);
            
            window.h5sdk.error(err => {
                showLoader(false);
                console.error('h5sdk error:', JSON.stringify(err));
                showErrorMessage('SDK初始化失败，请重试');
            });
            
            window.h5sdk.ready(() => {
                console.log("window.h5sdk.ready");
                console.log("url:", window.location.href);
                
                tt.requestAccess({
                    appID: res1.appid,
                    scopeList: [],
                    success(res) {
                        console.log("getAuthCode succeed");
                        
                        fetch(`/api/auth/callback?code=${res.code}`)
                            .then(response2 => response2.json())
                            .then(res2 => {
                                console.log("getUserInfo succeed");
                                showUser(res2);
                                showLoader(false);
                                initPageAnimations();
                            })
                            .catch(function (e) {
                                console.error(e);
                                showLoader(false);
                                showErrorMessage('获取用户信息失败，请重试');
                            });
                    },
                    fail(err) {
                        console.log(`getAuthCode failed, err:`, JSON.stringify(err));
                        showLoader(false);
                        showErrorMessage('认证失败，请重试');
                    }
                });
            });
        })
        .catch(function (e) {
            console.error(e);
            showLoader(false);
            showErrorMessage('网络连接失败，请检查网络后重试');
        });
}

/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-toast';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(231, 76, 60, 0.9);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000;
        backdrop-filter: blur(10px);
        animation: slideDown 0.3s ease;
    `;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
        errorDiv.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 300);
    }, 3000);
}

/**
 * 显示用户信息
 * @param {Object} res - 用户信息对象
 */
function showUser(res) {
    const imgDiv = document.getElementById('img_div');
    const nameDiv = document.getElementById('hello_text_name');
    const welcomeDiv = document.getElementById('hello_text_welcome');
    
    // 头像展示
    if (res.avatar_url) {
        imgDiv.innerHTML = `<img src="${res.avatar_url}" width="100%" height="100%" alt="用户头像"/>`;
        
        // 添加头像加载动画
        const img = imgDiv.querySelector('img');
        if (img) {
            img.onload = function() {
                this.style.opacity = '0';
                this.style.transform = 'scale(0.8)';
                this.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    this.style.opacity = '1';
                    this.style.transform = 'scale(1)';
                }, 100);
            };
        }
    }
    
    // 用户名称展示
    const userName = lang === "zh_CN" || lang === "zh-CN" ? res.name : res.en_name;
    if (userName && nameDiv) {
        typeWriter(nameDiv, userName, 50);
    }
    
    // 欢迎语展示
    const welcomeText = lang === "zh_CN" || lang === "zh-CN" ? "欢迎使用飞书" : "Welcome to Feishu";
    setTimeout(() => {
        if (welcomeDiv) {
            typeWriter(welcomeDiv, welcomeText, 30);
        }
    }, 800);
    
    // 初始化页面动画
    setTimeout(() => {
        initPageAnimations();
    }, 200);
}

/**
 * 打字机效果
 * @param {Element} element - 目标元素
 * @param {string} text - 要显示的文本
 * @param {number} speed - 打字速度（毫秒）
 */
function typeWriter(element, text, speed = 50) {
    if (!element || !text) return;
    
    element.textContent = '';
    element.style.opacity = '1';
    
    let i = 0;
    const timer = setInterval(() => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, speed);
}

// 添加动画样式到页面
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 预设样式
    const warning = document.getElementById('warning');
    if (warning) {
        warning.style.transition = 'all 0.3s ease';
        warning.style.opacity = '0';
        warning.style.transform = 'translateY(-20px)';
    }
    
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.transition = 'opacity 0.3s ease';
        loader.style.opacity = '0';
    }
});