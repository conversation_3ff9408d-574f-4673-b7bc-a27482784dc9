import requests

APP_ACCESS_TOKEN_URI = "/open-apis/auth/v3/app_access_token/internal"
USER_ACCESS_TOKEN_URI = "/open-apis/authen/v1/access_token"
USER_INFO_URI = "/open-apis/authen/v1/user_info"

class Auth(object):
    def __init__(self, feishu_host, app_id, app_secret):
        """初始化Auth对象"""
        self.feishu_host = feishu_host
        self.app_id = app_id
        self.app_secret = app_secret
        self._app_access_token = ""
        self._user_access_token = ""

    @property
    def app_access_token(self):
        """获取应用访问令牌"""
        return self._app_access_token

    @property
    def user_access_token(self):
        """获取用户访问令牌"""
        return self._user_access_token

    def authorize_app_access_token(self):
        """授权应用访问令牌"""
        url = self._gen_url(APP_ACCESS_TOKEN_URI)
        req_body = {"app_id": self.app_id, "app_secret": self.app_secret}
        response = requests.post(url, req_body)
        Auth._check_error_response(response)
        self._app_access_token = response.json().get("app_access_token")

    def authorize_user_access_token(self, code):
        """授权用户访问令牌"""
        self.authorize_app_access_token()
        url = self._gen_url(USER_ACCESS_TOKEN_URI)
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + self.app_access_token,
        }
        req_body = {"grant_type": "authorization_code", "code": code}
        response = requests.post(url=url, headers=headers, json=req_body)
        Auth._check_error_response(response)
        self._user_access_token = response.json().get("data").get("access_token")

    def get_user_info(self):
        """获取用户信息"""
        url = self._gen_url(USER_INFO_URI)
        headers = {
            "Authorization": "Bearer " + self.user_access_token,
            "Content-Type": "application/json",
        }
        response = requests.get(url=url, headers=headers)
        Auth._check_error_response(response)
        return response.json().get("data")

    def _gen_url(self, uri):
        """生成URL"""
        return "{host}{uri}".format(host=self.feishu_host, uri=uri)

    @staticmethod
    def _check_error_response(response):
        """检查响应错误"""
        if response.status_code != 200:
            raise Exception(response.content)
        response_json = response.json()
        code = response_json.get("code", -1)
        if code != 0:
            raise Exception(response.content)
