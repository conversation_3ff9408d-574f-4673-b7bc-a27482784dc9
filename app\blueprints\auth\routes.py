"""
认证相关路由
实现与前端auth.js对接的所有认证接口
"""
import logging
from sanic import Blueprint, Request
from sanic.response import JSONResponse
from sqlalchemy.orm import Session

from config import config
from app.core.database import get_db
from app.core.auth_manager import auth_manager
from app.core.exceptions import (
    AuthenticationError, ValidationError,
    ExternalServiceError, BaseAPIException
)
from app.utils.response_helper import success_response, error_response
from app.utils.decorators import require_auth
from .services import feishu_auth_service, user_service

logger = logging.getLogger(__name__)

# 创建认证蓝图
auth_bp = Blueprint("auth", url_prefix="/api/auth")


@auth_bp.route("/get_appid", methods=["GET"])
async def get_appid(request: Request) -> JSONResponse:
    """
    获取飞书应用ID
    对应前端auth.js中的getAppId函数

    Returns:
        JSONResponse: 包含appid的响应
    """
    try:
        logger.info("获取飞书应用ID请求")

        if not config.FEISHU_APP_ID:
            raise ValidationError("飞书应用ID未配置")

        return success_response(data={"appid": config.FEISHU_APP_ID})

    except BaseAPIException as e:
        logger.error(f"获取应用ID失败: {e.message}")
        return error_response(message=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取应用ID时发生未知错误: {e}")
        return error_response(message="服务器内部错误", code=500)


@auth_bp.route("/callback", methods=["GET"])
async def feishu_callback(request: Request) -> JSONResponse:
    """
    飞书OAuth回调接口
    对应前端auth.js中的feishuCallback函数

    Args:
        code: 飞书授权码（查询参数）

    Returns:
        JSONResponse: 包含用户信息和token的响应
    """
    try:
        # 获取授权码
        code = request.args.get("code")
        if not code:
            raise ValidationError("授权码不能为空")

        logger.info(f"处理飞书回调，授权码: {code[:10]}...")

        # 获取数据库会话
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # 执行用户认证
            user, access_token, refresh_token = user_service.authenticate_user(code, db)

            # 构建响应数据
            response_data = {
                **user.to_dict(),
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "Bearer",
                "expires_in": config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }

            logger.info(f"用户认证成功: {user.name}")
            return success_response(data=response_data)

        finally:
            db.close()

    except BaseAPIException as e:
        logger.error(f"飞书回调失败: {e.message}")
        return error_response(message=e.message, code=e.code)
    except Exception as e:
        logger.error(f"飞书回调时发生未知错误: {e}")
        return error_response(message="认证失败，请重试", code=500)


@auth_bp.route("/validate", methods=["GET"])
async def validate_session(request: Request) -> JSONResponse:
    """
    验证session token有效性
    对应前端auth.js中的validateSession函数

    Headers:
        Authorization: Bearer <token>

    Returns:
        JSONResponse: 用户信息或错误信息
    """
    try:
        # 获取Authorization头
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise AuthenticationError("缺少有效的认证令牌")

        # 提取token
        token = auth_header.split(" ")[1]

        # 验证token
        payload = auth_manager.verify_access_token(token)
        user_id = payload.get("user_id")

        if not user_id:
            raise AuthenticationError("无效的令牌")

        # 获取用户信息
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            user = user_service.get_user_by_id(user_id, db)
            if not user:
                raise AuthenticationError("用户不存在")

            logger.info(f"令牌验证成功: {user.name}")
            return success_response(data=user.to_dict())

        finally:
            db.close()

    except BaseAPIException as e:
        logger.warning(f"令牌验证失败: {e.message}")
        return error_response(message=e.message, code=e.code)
    except Exception as e:
        logger.error(f"令牌验证时发生未知错误: {e}")
        return error_response(message="令牌验证失败", code=401)


@auth_bp.route("/refresh", methods=["POST"])
async def refresh_session(request: Request) -> JSONResponse:
    """
    刷新session token
    对应前端auth.js中的refreshSession函数

    Body:
        {
            "refresh_token": "refresh_token_string"
        }

    Returns:
        JSONResponse: 新的session信息
    """
    try:
        # 获取请求数据
        data = request.json or {}
        refresh_token = data.get("refresh_token")

        if not refresh_token:
            raise ValidationError("Refresh token不能为空")

        # 验证refresh token
        user_id = auth_manager.verify_refresh_token(refresh_token)

        # 获取用户信息
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            user = user_service.get_user_by_id(user_id, db)
            if not user:
                raise AuthenticationError("用户不存在")

            # 生成新的token
            new_access_token = auth_manager.generate_access_token(user.to_dict())
            new_refresh_token = auth_manager.generate_refresh_token(user.id)

            # 撤销旧的refresh token
            auth_manager.revoke_refresh_token(refresh_token)

            # 更新会话信息
            user_service._save_user_session(user.id, new_access_token, new_refresh_token, db)

            response_data = {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "Bearer",
                "expires_in": config.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }

            logger.info(f"令牌刷新成功: {user.name}")
            return success_response(data=response_data)

        finally:
            db.close()

    except BaseAPIException as e:
        logger.warning(f"令牌刷新失败: {e.message}")
        return error_response(message=e.message, code=e.code)
    except Exception as e:
        logger.error(f"令牌刷新时发生未知错误: {e}")
        return error_response(message="刷新登录状态失败，请重新登录", code=401)


@auth_bp.route("/logout", methods=["POST"])
async def logout(request: Request) -> JSONResponse:
    """
    用户登出
    对应前端auth.js中的logout函数

    Headers:
        Authorization: Bearer <token> (可选)

    Returns:
        JSONResponse: 登出结果
    """
    try:
        # 获取Authorization头
        auth_header = request.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]

            try:
                # 验证token并获取用户信息
                payload = auth_manager.verify_access_token(token)
                user_id = payload.get("user_id")

                if user_id:
                    # 将access token加入黑名单
                    auth_manager.blacklist_token(token)

                    # 获取并撤销refresh token
                    db_gen = get_db()
                    db: Session = next(db_gen)

                    try:
                        # 删除用户会话
                        db.query(UserSession).filter(UserSession.user_id == user_id).delete()
                        db.commit()

                        logger.info(f"用户登出成功: user_id={user_id}")

                    finally:
                        db.close()

            except Exception as e:
                # 即使token验证失败也继续登出流程
                logger.warning(f"登出时token验证失败: {e}")

        return success_response(message="登出成功")

    except Exception as e:
        logger.error(f"登出时发生错误: {e}")
        # 即使发生错误也返回成功，因为前端需要清理本地状态
        return success_response(message="登出成功")


@auth_bp.route("/user", methods=["GET"])
@require_auth
async def get_current_user(request: Request) -> JSONResponse:
    """
    获取当前用户信息
    对应前端auth.js中的getCurrentUser函数

    Headers:
        Authorization: Bearer <token>

    Returns:
        JSONResponse: 用户信息
    """
    try:
        # 从请求中获取用户信息（由require_auth装饰器注入）
        user_info = getattr(request.ctx, 'user', None)

        if not user_info:
            raise AuthenticationError("用户信息获取失败")

        logger.info(f"获取用户信息成功: {user_info.get('name')}")
        return success_response(data=user_info)

    except BaseAPIException as e:
        logger.warning(f"获取用户信息失败: {e.message}")
        return error_response(message=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取用户信息时发生未知错误: {e}")
        return error_response(message="获取用户信息失败", code=500)