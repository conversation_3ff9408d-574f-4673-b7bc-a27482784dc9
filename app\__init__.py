"""
Sanic应用工厂
创建和配置Sanic应用实例
"""
import logging
from sanic import Sanic
from sanic_cors import CORS

from config import config
from app.core.database import db_manager
from app.core.redis_client import redis_manager
from app.blueprints.auth.routes import auth_bp


def create_app() -> Sanic:
    """
    创建Sanic应用实例

    Returns:
        Sanic: 配置好的应用实例
    """
    # 创建Sanic应用
    app = Sanic("FileManagementSystem")

    # 配置应用
    app.config.update({
        "DEBUG": config.DEBUG,
        "ACCESS_LOG": config.DEBUG,
        "AUTO_RELOAD": config.DEBUG,
    })

    # 配置CORS
    CORS(
        app,
        origins=config.CORS_ORIGINS,
        methods=config.CORS_METHODS,
        headers=config.CORS_HEADERS,
        automatic_options=True
    )

    # 配置日志
    setup_logging()

    # 注册蓝图
    register_blueprints(app)

    # 注册中间件
    register_middleware(app)

    # 注册事件监听器
    register_listeners(app)

    return app


def register_blueprints(app: Sanic):
    """
    注册蓝图

    Args:
        app: Sanic应用实例
    """
    # 注册认证蓝图
    app.blueprint(auth_bp)

    # TODO: 注册其他蓝图
    # app.blueprint(file_management_bp)
    # app.blueprint(common_bp)


def register_middleware(app: Sanic):
    """
    注册中间件

    Args:
        app: Sanic应用实例
    """

    @app.middleware("request")
    async def add_cors_headers(request, response):
        """添加CORS头"""
        pass

    @app.middleware("response")
    async def add_security_headers(request, response):
        """添加安全头"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"


def register_listeners(app: Sanic):
    """
    注册事件监听器

    Args:
        app: Sanic应用实例
    """

    @app.before_server_start
    async def setup_db(app, loop):
        """服务器启动前设置数据库"""
        logger = logging.getLogger(__name__)

        # 检查数据库连接
        if db_manager.check_connection():
            logger.info("数据库连接正常")
        else:
            logger.error("数据库连接失败")
            raise Exception("数据库连接失败")

        # 检查Redis连接
        if redis_manager.is_connected():
            logger.info("Redis连接正常")
        else:
            logger.warning("Redis连接失败，部分功能可能受影响")

    @app.after_server_stop
    async def cleanup(app, loop):
        """服务器停止后清理资源"""
        logger = logging.getLogger(__name__)
        logger.info("应用正在关闭...")


def setup_logging():
    """
    配置日志系统
    """
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),  # 控制台输出
            # logging.FileHandler(config.LOG_FILE)  # 文件输出
        ]
    )

    # 设置第三方库的日志级别
    logging.getLogger("sanic").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.WARNING)