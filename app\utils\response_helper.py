"""
响应格式化工具
提供统一的API响应格式
"""
from datetime import datetime
from typing import Any, Optional, Dict
from sanic.response import JSONResponse


def success_response(
    data: Any = None,
    message: str = "success",
    code: int = 200
) -> JSONResponse:
    """
    成功响应格式

    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码

    Returns:
        JSONResponse: 格式化的成功响应
    """
    response_data = {
        "code": code,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }

    if data is not None:
        response_data["data"] = data

    return JSONResponse(response_data, status=code)


def error_response(
    message: str = "error",
    code: int = 400,
    error: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> JSONResponse:
    """
    错误响应格式

    Args:
        message: 错误消息
        code: 状态码
        error: 详细错误信息
        details: 额外的错误详情

    Returns:
        JSONResponse: 格式化的错误响应
    """
    response_data = {
        "code": code,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }

    if error:
        response_data["error"] = error

    if details:
        response_data["details"] = details

    return JSONResponse(response_data, status=code)


def paginated_response(
    data: list,
    total: int,
    page: int = 1,
    page_size: int = 20,
    message: str = "success"
) -> JSONResponse:
    """
    分页响应格式

    Args:
        data: 数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页大小
        message: 响应消息

    Returns:
        JSONResponse: 格式化的分页响应
    """
    total_pages = (total + page_size - 1) // page_size

    response_data = {
        "code": 200,
        "message": message,
        "data": data,
        "pagination": {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        },
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }

    return JSONResponse(response_data, status=200)