"""
数据库连接管理模块
提供SQLAlchemy数据库连接和会话管理
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from config import config

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    config.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=config.DEBUG,  # 开发环境显示SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据对象
metadata = MetaData()


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal

    def get_session(self) -> Generator[Session, None, None]:
        """
        获取数据库会话

        Yields:
            Session: 数据库会话对象
        """
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            logger.error(f"数据库会话错误: {e}")
            session.rollback()
            raise
        finally:
            session.close()

    def create_tables(self):
        """创建所有表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise

    def drop_tables(self):
        """删除所有表"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"删除数据库表失败: {e}")
            raise

    def check_connection(self) -> bool:
        """
        检查数据库连接是否正常

        Returns:
            bool: 连接状态
        """
        try:
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("数据库连接正常")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False


# 创建数据库管理器实例
db_manager = DatabaseManager()


def get_db() -> Generator[Session, None, None]:
    """
    依赖注入函数，用于获取数据库会话

    Yields:
        Session: 数据库会话对象
    """
    yield from db_manager.get_session()